# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a full-stack WeChat mini-program project for a domestic service management platform (金刚到家系统). The system manages household services with dual-role support for merchants and service staff.

### Technology Stack

**Backend (storeapi/)**
- FastAPI + Python 3.x with async/await
- SQLAlchemy 2.0 async ORM with MySQL/PostgreSQL
- Redis for caching and session management
- JWT authentication with bcrypt password hashing
- Three-layer architecture: Controller → Service → DAO → Entity

**Frontend (storeui/)**
- UniApp framework (Vue.js 2.x based) for multi-platform deployment
- uView UI component library
- Vuex for state management
- Cross-platform: WeChat mini-program, H5, mobile apps

## Development Commands

### Backend (storeapi/)
```bash
# Install dependencies
pip install -r requirements.txt

# Run development server
python app.py
# or
uvicorn server:app --reload

# Code formatting and linting
ruff format .
ruff check .
```

### Frontend (storeui/)
```bash
# Install dependencies
npm install

# Development
npm run serve              # Alias for dev:h5
npm run dev:h5            # H5 development
npm run dev:mp-weixin     # WeChat mini-program development

# Production builds
npm run build             # Alias for build:h5
npm run build:h5          # H5 production build
npm run build:mp-weixin   # WeChat mini-program build

# Testing
npm run test:h5
npm run test:mp-weixin
```

## Architecture Overview

### Backend Structure
- **Controllers** (`module_admin/controller/`): API endpoints and request handling
- **Services** (`module_admin/service/`): Business logic implementation
- **DAOs** (`module_admin/dao/`): Database access layer
- **Entities** (`module_admin/entity/`): Data models (DO/VO objects)
- **Config** (`config/`): Database, Redis, environment configuration
- **Utils** (`utils/`): Shared utilities for file upload, OCR, WeChat integration

### Frontend Structure
- **Pages** (`src/pages/`): Main application pages organized by feature modules
- **API** (`src/api/`): HTTP request wrappers for backend communication
- **Components** (`src/components/`): Reusable Vue components with xyj- prefix
- **Utils** (`src/utlis/`): Authentication, city data, common utilities
- **Vuex** (`src/vuex/`): Centralized state management

### Key Business Modules
1. **Order Management**: Complete order lifecycle from creation to payment
2. **Staff Management**: Service staff profiles, schedules, and assignments
3. **Customer Management**: Client information and service history
4. **Store Management**: Multi-store operations and product catalogs
5. **Marketing**: Poster generation, promotion codes, video sharing
6. **Insurance**: Staff insurance and policy management

## Configuration

### Environment Configuration
- Backend: `storeapi/config/env.py` - Database, Redis, API settings
- Frontend: `storeui/src/setConfig.js` - API endpoints and environment switching

### Database
- Uses SQLAlchemy async with connection pooling
- Database migrations in `storeapi/database_migrations/`
- Models follow snake_case naming convention

### Authentication
- JWT-based authentication with role-based access control
- WeChat OAuth integration for mini-program login
- Separate authentication flows for merchants and staff

## Code Conventions

### Backend
- Follow FastAPI best practices with async/await
- Use Pydantic models for request/response validation
- Three-layer architecture separation
- Snake_case for Python variables and functions
- Type hints required for all function signatures

### Frontend
- Vue.js 2.x composition with UniApp conventions
- Component naming with xyj- prefix for custom components
- CamelCase for JavaScript variables and functions
- Use uView UI components when possible

## External Integrations

- **WeChat**: Mini-program APIs, official account messaging
- **Payment**: WeChat Pay, Alipay integration
- **Cloud Services**: Huawei Cloud OBS for file storage
- **AI Services**: Baidu OCR for document processing
- **Maps**: Location and address services

## Development Notes

- The project supports both merchant-side and staff-side interfaces
- Share link system uses 8-character short codes for order/lead sharing
- File uploads go through unified upload service with cloud storage
- All datetime handling uses server-side timestamps
- WeChat mini-program specific APIs are abstracted in utility functions