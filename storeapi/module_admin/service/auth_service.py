import jwt
import uuid
from datetime import datetime, timedelta, timezone
from fastapi import Request
from sqlalchemy import update
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Optional
from config.enums import RedisInitKeyConfig
from config.env import AppConfig, JwtConfig
from exceptions.exception import LoginException, AuthException, BusinessException
from module_admin.dao.internal_user_dao import InternalUserDao
from module_admin.entity.do.internal_user import InternalUser
from utils.log_util import logger
from utils.pwd_util import PwdUtil

class AuthService:
    """认证服务"""

    @classmethod
    async def login_service(cls, request: Request, query_db: AsyncSession, mobile: str, password: str) -> Dict:
        """
        用户登录服务

        :param request: Request对象
        :param query_db: 数据库会话
        :param mobile: 手机号
        :param password: 密码
        :return: 登录结果
        """
        # 创建一个新的事务
        async with query_db.begin():
            try:
                logger.info(f'开始用户登录，手机号: {mobile}')

                # 检查账号是否被锁定
                logger.info('检查账号是否被锁定')
                account_lock = await request.app.state.redis.get(
                    f'{RedisInitKeyConfig.ACCOUNT_LOCK.key}:{mobile}'
                )
                if mobile == account_lock:
                    logger.warning(f'账号 {mobile} 已被锁定，请稍后再试')
                    raise LoginException(data='', message='账号已锁定，请稍后再试')
                logger.info('账号未锁定，继续验证')

                # 验证用户 - 使用显式的异步上下文
                logger.info('开始查询用户信息')
                try:
                    user = await InternalUserDao.get_internal_user_by_mobile(query_db, mobile)
                    if not user:
                        logger.warning(f'手机号 {mobile} 对应的用户不存在')
                        raise LoginException(data='', message='用户不存在')
                    logger.info(f'用户查询成功，用户ID: {user.id}')
                except Exception as db_error:
                    logger.error(f'查询用户信息时发生异常: {str(db_error)}')
                    # 回滚事务并重新抛出异常
                    await query_db.rollback()
                    raise BusinessException(message=f'查询用户信息失败: {str(db_error)}')

                # 验证密码
                logger.info('开始验证用户密码')
                if not PwdUtil.verify_password(password, user.password):
                    # 密码错误次数控制
                    logger.info('密码验证失败，检查错误次数')
                    cache_password_error_count = await request.app.state.redis.get(
                        f'{RedisInitKeyConfig.PASSWORD_ERROR_COUNT.key}:{mobile}'
                    )
                    password_error_counted = 0 if not cache_password_error_count else int(cache_password_error_count)
                    password_error_count = password_error_counted + 1
                    logger.info(f'当前密码错误次数: {password_error_count}')

                    # 设置密码错误次数
                    await request.app.state.redis.set(
                        f'{RedisInitKeyConfig.PASSWORD_ERROR_COUNT.key}:{mobile}',
                        password_error_count,
                        ex=timedelta(minutes=10),
                    )
                    logger.info('已更新密码错误次数')

                    # 超过5次锁定账号
                    if password_error_count > 5:
                        logger.warning(f'密码错误次数超过5次，锁定账号 {mobile}')
                        await request.app.state.redis.delete(
                            f'{RedisInitKeyConfig.PASSWORD_ERROR_COUNT.key}:{mobile}'
                        )
                        await request.app.state.redis.set(
                            f'{RedisInitKeyConfig.ACCOUNT_LOCK.key}:{mobile}',
                            mobile,
                            ex=timedelta(minutes=10),
                        )
                        logger.warning('账号已锁定，10分钟后可重试')
                        raise LoginException(data='', message='10分钟内密码已输错超过5次，账号已锁定，请10分钟后再试')

                    logger.warning('密码错误')
                    raise LoginException(data='', message='密码错误')
                logger.info('密码验证成功')

                # 验证用户状态
                logger.info(f'检查用户状态: {user.status}')
                if user.status == '2':  # 冻结状态
                    logger.warning(f'用户 {mobile} 已被冻结')
                    raise LoginException(data='', message='用户已被冻结')
                elif user.status == '3':  # 离职状态
                    logger.warning(f'用户 {mobile} 已离职')
                    raise LoginException(data='', message='用户已离职')
                logger.info('用户状态正常')

                # 清除密码错误次数
                logger.info('清除密码错误次数记录')
                await request.app.state.redis.delete(f'{RedisInitKeyConfig.PASSWORD_ERROR_COUNT.key}:{mobile}')

                # 生成会话ID
                session_id = str(uuid.uuid4())
                logger.info(f'生成session_id: {session_id}')

                # 生成访问令牌
                user_id = str(user.id)
                access_token = await cls.create_access_token(
                    data={
                        'user_id': user_id,
                        'session_id': session_id
                    },
                    expires_delta=timedelta(minutes=JwtConfig.jwt_redis_expire_minutes)
                )
                logger.info('创建token成功')

                # 存储令牌到Redis
                if AppConfig.app_same_time_login:
                    await request.app.state.redis.set(
                        f'{RedisInitKeyConfig.ACCESS_TOKEN.key}:{session_id}',
                        access_token,
                        ex=timedelta(minutes=JwtConfig.jwt_redis_expire_minutes)
                    )
                else:
                    await request.app.state.redis.set(
                        f'{RedisInitKeyConfig.ACCESS_TOKEN.key}:{user_id}',
                        access_token,
                        ex=timedelta(minutes=JwtConfig.jwt_redis_expire_minutes)
                    )
                logger.info('存储token成功')

                # 更新用户最后登录信息 - 使用 update 语句而不是直接修改对象
                current_time = datetime.now()
                client_ip = request.client.host
                user_agent = request.headers.get('User-Agent', '')

                try:
                    # 使用 update 语句更新用户信息
                    logger.info(f'开始更新用户最后登录信息，用户ID: {user.id}')
                    update_stmt = (
                        update(InternalUser)
                        .where(InternalUser.id == user.id)
                        .values(
                            last_login_time=current_time,
                            last_login_ip=client_ip,
                            last_login_device=user_agent
                        )
                    )
                    await query_db.execute(update_stmt)
                    # 不需要显式提交，因为我们使用了 with query_db.begin()
                    logger.info('用户最后登录信息更新成功')
                except Exception as update_error:
                    logger.error(f'更新用户最后登录信息时发生异常: {str(update_error)}')
                    # 回滚事务并重新抛出异常
                    await query_db.rollback()
                    raise BusinessException(message=f'更新用户信息失败: {str(update_error)}')

                # 更新本地对象以便后续使用
                user.last_login_time = current_time
                user.last_login_ip = client_ip
                user.last_login_device = user_agent

                # 构建返回数据
                result = {
                    "user_uuid": user.uuid,
                    "user_mobile": user.mobile,
                    "user_number": user.number,
                    "user_name": user.name,
                    "user_nick_name": user.nick_name,
                    "user_avatar": user.avatar,
                    "user_token": access_token,
                    "jpush_registration_id": user.jpush_registration_id,
                    "role_id": user.role_id,
                    "role_name": user.role_name,
                    "title": user.title,
                    "company_name": user.company_name,
                    "company_number": user.number,
                    "company_server_name": "家政员",
                    "aunt_share_name": "full_name",
                    "store_uuid": user.store_uuid,
                    "store_name": user.store_name,
                    "user_count": 0,
                    "store_logo": "",
                    "avatar_share": user.avatar,
                    "store_number": "",
                    "user_qrcode": None,
                    "theme_color": "#98C515",
                    "send_order_mobile": user.mobile,
                    "job_number": user.number,
                    "id_number": user.id_number,
                    "age": user.age,
                    "im_login_info": {
                        "imUserId": user.number,
                        "imUserSig": ""
                    },
                    "store_formats": ["1", "2"],
                    "city_id": user.city_id,
                    "city_name": user.city,
                    "province_id": "",
                    "province_name": "",
                    "company_customer_level": "1",
                    "cloud_plus_version": "1.0",
                    "cloud_plus_settlement_ratio": "0.00"
                }

                logger.info('用户登录成功')
                return result

            except (LoginException, AuthException) as e:
                # 这些异常已经包含了详细信息，直接向上传递
                # 不需要回滚事务，因为这些是业务逻辑异常，不是数据库异常
                raise e
            except Exception as e:
                # 记录未预期的异常
                logger.error(f'用户登录过程中发生未预期的异常: {str(e)}')
                # 确保事务被回滚
                await query_db.rollback()
                raise BusinessException(message=f'登录失败: {str(e)}')

    @classmethod
    async def login_by_code_service(cls, request: Request, query_db: AsyncSession, mobile: str, code: str) -> Dict:
        """
        验证码登录服务

        :param request: Request对象
        :param query_db: 数据库会话
        :param mobile: 手机号
        :param code: 短信验证码
        :return: 登录结果
        """
        # 创建一个新的事务
        async with query_db.begin():
            try:
                logger.info(f'开始验证码登录，手机号: {mobile}')

                # 检查账号是否被锁定
                logger.info('检查账号是否被锁定')
                account_lock = await request.app.state.redis.get(
                    f'{RedisInitKeyConfig.ACCOUNT_LOCK.key}:{mobile}'
                )
                if mobile == account_lock:
                    logger.warning(f'账号 {mobile} 已被锁定，请稍后再试')
                    raise LoginException(data='', message='账号已锁定，请稍后再试')
                logger.info('账号未锁定，继续验证')

                # 验证短信验证码
                logger.info('开始验证短信验证码')
                stored_code = await request.app.state.redis.get(
                    f'{RedisInitKeyConfig.SMS_CODE.key}:{mobile}'
                )
                if not stored_code:
                    logger.warning(f'手机号 {mobile} 的验证码不存在或已过期')
                    raise LoginException(data='', message='验证码不存在或已过期')

                if str(stored_code) != str(code):
                    logger.warning(f'手机号 {mobile} 的验证码错误')
                    raise LoginException(data='', message='验证码错误')
                logger.info('验证码验证成功')

                # 验证码使用后立即删除
                logger.info('删除已使用的验证码')
                await request.app.state.redis.delete(f'{RedisInitKeyConfig.SMS_CODE.key}:{mobile}')

                # 验证用户 - 使用显式的异步上下文
                logger.info('开始查询用户信息')
                try:
                    user = await InternalUserDao.get_internal_user_by_mobile(query_db, mobile)
                    if not user:
                        logger.warning(f'手机号 {mobile} 对应的用户不存在')
                        raise LoginException(data='', message='用户不存在')
                    logger.info(f'用户查询成功，用户ID: {user.id}')
                except Exception as db_error:
                    logger.error(f'查询用户信息时发生异常: {str(db_error)}')
                    # 回滚事务并重新抛出异常
                    await query_db.rollback()
                    raise BusinessException(message=f'查询用户信息失败: {str(db_error)}')

                # 验证用户状态
                logger.info(f'检查用户状态: {user.status}')
                if user.status == '2':  # 冻结状态
                    logger.warning(f'用户 {mobile} 已被冻结')
                    raise LoginException(data='', message='用户已被冻结')
                elif user.status == '3':  # 离职状态
                    logger.warning(f'用户 {mobile} 已离职')
                    raise LoginException(data='', message='用户已离职')
                logger.info('用户状态正常')

                # 清除密码错误次数（验证码登录成功后也清除）
                logger.info('清除密码错误次数记录')
                await request.app.state.redis.delete(f'{RedisInitKeyConfig.PASSWORD_ERROR_COUNT.key}:{mobile}')

                # 生成会话ID
                session_id = str(uuid.uuid4())
                logger.info(f'生成session_id: {session_id}')

                # 生成访问令牌
                user_id = str(user.id)
                access_token = await cls.create_access_token(
                    data={
                        'user_id': user_id,
                        'session_id': session_id
                    },
                    expires_delta=timedelta(minutes=JwtConfig.jwt_redis_expire_minutes)
                )
                logger.info('创建token成功')

                # 存储令牌到Redis
                if AppConfig.app_same_time_login:
                    await request.app.state.redis.set(
                        f'{RedisInitKeyConfig.ACCESS_TOKEN.key}:{session_id}',
                        access_token,
                        ex=timedelta(minutes=JwtConfig.jwt_redis_expire_minutes)
                    )
                else:
                    await request.app.state.redis.set(
                        f'{RedisInitKeyConfig.ACCESS_TOKEN.key}:{user_id}',
                        access_token,
                        ex=timedelta(minutes=JwtConfig.jwt_redis_expire_minutes)
                    )
                logger.info('存储token成功')

                # 更新用户最后登录信息 - 使用 update 语句而不是直接修改对象
                current_time = datetime.now()
                client_ip = request.client.host
                user_agent = request.headers.get('User-Agent', '')

                try:
                    # 使用 update 语句更新用户信息
                    logger.info(f'开始更新用户最后登录信息，用户ID: {user.id}')
                    update_stmt = (
                        update(InternalUser)
                        .where(InternalUser.id == user.id)
                        .values(
                            last_login_time=current_time,
                            last_login_ip=client_ip,
                            last_login_device=user_agent
                        )
                    )
                    await query_db.execute(update_stmt)
                    # 不需要显式提交，因为我们使用了 with query_db.begin()
                    logger.info('用户最后登录信息更新成功')
                except Exception as update_error:
                    logger.error(f'更新用户最后登录信息时发生异常: {str(update_error)}')
                    # 回滚事务并重新抛出异常
                    await query_db.rollback()
                    raise BusinessException(message=f'更新用户信息失败: {str(update_error)}')

                # 更新本地对象以便后续使用
                user.last_login_time = current_time
                user.last_login_ip = client_ip
                user.last_login_device = user_agent

                # 构建返回数据（与密码登录保持一致的格式）
                result = {
                    "user_uuid": user.uuid,
                    "user_mobile": user.mobile,
                    "user_number": user.number,
                    "user_name": user.name,
                    "user_nick_name": user.nick_name,
                    "user_avatar": user.avatar,
                    "user_token": access_token,
                    "jpush_registration_id": user.jpush_registration_id,
                    "role_id": user.role_id,
                    "role_name": user.role_name,
                    "title": user.title,
                    "company_name": user.company_name,
                    "company_number": user.number,
                    "company_server_name": "家政员",
                    "aunt_share_name": "full_name",
                    "store_uuid": user.store_uuid,
                    "store_name": user.store_name,
                    "user_count": 0,
                    "store_logo": "",
                    "avatar_share": user.avatar,
                    "store_number": "",
                    "user_qrcode": None,
                    "theme_color": "#98C515",
                    "send_order_mobile": user.mobile,
                    "job_number": user.number,
                    "id_number": user.id_number,
                    "age": user.age,
                    "im_login_info": {
                        "imUserId": user.number,
                        "imUserSig": ""
                    },
                    "store_formats": ["1", "2"],
                    "city_id": user.city_id,
                    "city_name": user.city,
                    "province_id": "",
                    "province_name": "",
                    "company_customer_level": "1",
                    "cloud_plus_version": "1.0",
                    "cloud_plus_settlement_ratio": "0.00"
                }

                logger.info('验证码登录成功')
                return result

            except (LoginException, AuthException) as e:
                # 这些异常已经包含了详细信息，直接向上传递
                # 不需要回滚事务，因为这些是业务逻辑异常，不是数据库异常
                raise e
            except Exception as e:
                # 记录未预期的异常
                logger.error(f'验证码登录过程中发生未预期的异常: {str(e)}')
                # 确保事务被回滚
                await query_db.rollback()
                raise BusinessException(message=f'验证码登录失败: {str(e)}')

    @classmethod
    async def create_access_token(cls, data: dict, expires_delta: Optional[timedelta] = None):
        """
        创建访问令牌

        :param data: 令牌数据
        :param expires_delta: 过期时间
        :return: 访问令牌
        """
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + (expires_delta or timedelta(minutes=JwtConfig.jwt_expire_minutes))
        to_encode.update({'exp': expire})
        encoded_jwt = jwt.encode(to_encode, JwtConfig.jwt_secret_key, algorithm=JwtConfig.jwt_algorithm)
        return encoded_jwt

    @classmethod
    async def get_setting_service(cls, query_db: AsyncSession) -> Dict:
        """
        获取认证设置服务

        :param query_db: 数据库会话
        :return: 认证设置
        """
        try:
            # 这里可以从数据库或配置中获取认证设置
            # 为简化示例，这里直接返回固定值
            return {
                "login_type": "password",
                "captcha_enabled": False,
                "sms_login_enabled": True,
                "remember_me_enabled": True,
                "max_login_attempts": 5,
                "lock_duration_minutes": 10
            }
        except Exception as e:
            logger.error(f'获取认证设置失败: {str(e)}')
            raise BusinessException(message=f'获取认证设置失败: {str(e)}')

    @classmethod
    async def get_boot_log_service(cls, query_db: AsyncSession) -> Dict:
        """
        获取启动日志服务

        :param query_db: 数据库会话
        :return: 启动日志
        """
        try:
            # 这里可以从数据库或日志文件中获取启动日志
            # 为简化示例，这里直接返回固定值
            return {
                "app_version": "1.0.0",
                "boot_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "system_status": "normal",
                "message": "系统启动正常"
            }
        except Exception as e:
            logger.error(f'获取启动日志失败: {str(e)}')
            raise BusinessException(message=f'获取启动日志失败: {str(e)}')

    @classmethod
    async def logout_service(cls, request: Request, token: str) -> Dict:
        """
        用户退出登录服务

        :param request: Request对象
        :param token: 用户token
        :return: 退出登录结果
        """
        try:
            logger.info('开始用户退出登录')

            # 解析token获取session_id和user_id（忽略过期时间）
            try:
                payload = jwt.decode(
                    token,
                    JwtConfig.jwt_secret_key,
                    algorithms=[JwtConfig.jwt_algorithm],
                    options={'verify_exp': False}  # 忽略过期时间，允许过期token退出登录
                )
                session_id = payload.get('session_id')
                user_id = payload.get('user_id')

                logger.info(f'解析token成功，session_id: {session_id}, user_id: {user_id}')

                # 删除Redis中的token
                if AppConfig.app_same_time_login:
                    redis_key = f'{RedisInitKeyConfig.ACCESS_TOKEN.key}:{session_id}'
                else:
                    redis_key = f'{RedisInitKeyConfig.ACCESS_TOKEN.key}:{user_id}'

                # 尝试删除Redis中的token（即使token已经不存在也不报错）
                deleted_count = await request.app.state.redis.delete(redis_key)
                if deleted_count > 0:
                    logger.info(f'已删除Redis中的token，key: {redis_key}')
                else:
                    logger.info(f'Redis中的token已不存在或已过期，key: {redis_key}')

            except jwt.InvalidTokenError as jwt_error:
                # token无效或格式错误，但仍然返回成功（因为退出登录的目的已达到）
                logger.warning(f'token解析失败，但退出登录仍视为成功: {str(jwt_error)}')

            logger.info('用户退出登录成功')
            return {'message': '退出登录成功'}

        except Exception as e:
            logger.error(f"用户退出登录异常: {str(e)}")
            # 对于退出登录，即使出现异常也返回成功，确保用户能够退出
            logger.info('退出登录过程中出现异常，但仍返回成功以确保用户能够退出')
            return {'message': '退出登录成功'}
