/**
 * 认证模块接口
 */
import { post, get } from '../utlis/require.js';
import store from '../vuex/index.js';

/**
 * 用户登录
 * @param {Object} data - 登录参数
 * @param {string} data.mobile - 手机号
 * @param {string} data.password - 密码
 * @returns {Promise} - 返回登录结果
 */
export const login = (data) => {
  const { mobile, password } = data;
  return post('/api/v1/auth/login', { mobile, password }).then(res => {
    // 处理响应数据，保存token到Vuex
    store.commit('Updates', { token: res.token });
    return res;
  }).catch(error => {
    console.error('Login Error:', error);
    throw error;
  });
};

/**
 * 获取认证设置
 * @returns {Promise} - 返回认证设置
 */
export const getAuthSetting = () => {
  return get('/api/v1/auth/setting').then(res => {
    return res;
  }).catch(error => {
    console.error('Get Auth Setting Error:', error);
    throw error;
  });
};

/**
 * 获取启动日志
 * @returns {Promise} - 返回启动日志
 */
export const getBootLog = () => {
  return get('/api/v1/auth/bootLog').then(res => {
    return res;
  }).catch(error => {
    console.error('Get Boot Log Error:', error);
    throw error;
  });
};

/**
 * 用户退出登录
 * @returns {Promise} - 返回退出登录结果
 */
export const logout = () => {
  return post('/api/v1/auth/logout', {}).then(res => {
    console.log('退出登录API调用成功:', res);
    return res;
  }).catch(error => {
    console.error('Logout API Error:', error);
    // 即使API调用失败，也不阻止退出流程
    throw error;
  });
};

// 导出所有接口函数
export default {
  login,
  getAuthSetting,
  getBootLog,
  logout
};
