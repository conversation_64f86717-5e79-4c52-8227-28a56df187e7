/**
 * 服务人员认证模块接口
 */
import { post, get } from '../utlis/require.js';
import store from '../vuex/index.js';

/**
 * 发送短信验证码
 * @param {Object} data - 参数
 * @param {string} data.mobile - 手机号
 * @returns {Promise} - 返回结果
 */
export const sendSmsCode = (data) => {
  const { mobile } = data;
  return post('/api/v1/staff-login/send-sms', { mobile });
};

/**
 * 服务人员密码登录
 * @param {Object} data - 登录参数
 * @param {string} data.mobile - 手机号
 * @param {string} data.password - 密码
 * @returns {Promise} - 返回登录结果
 */
export const staffPasswordLogin = (data) => {
  const { mobile, password } = data;
  return post('/api/v1/staff-login/password', { mobile, password }).then(res => {
    // 处理响应数据，保存token到Vuex
    if (res.access_token) {
      // 先清理所有旧的token，确保使用最新的token
      try {
        uni.removeStorageSync('staffToken');
        uni.removeStorageSync('access_token');
        uni.removeStorageSync('token');
        console.log('已清理旧的token');
      } catch (error) {
        console.warn('清理旧token失败:', error);
      }

      store.commit('Updates', {
        token: res.access_token,
        isLogin: true,
        currentRole: 'staff',
        staffInfo: res.staff_info,
        staffToken: res.access_token,
        access_token: res.access_token  // 确保access_token也被设置
      });

      // 保存到本地存储 - 员工端使用独立的token键
      uni.setStorageSync('staffToken', res.access_token);
      uni.setStorageSync('access_token', res.access_token); // 确保access_token也被保存
      uni.setStorageSync('token', res.access_token); // 兼容性保留
      uni.setStorageSync('isLogin', true);
      uni.setStorageSync('currentRole', 'staff');
      uni.setStorageSync('staffInfo', res.staff_info);

      console.log('员工密码登录成功，token已保存:', {
        staffToken: '已保存',
        currentRole: 'staff',
        staffInfo: res.staff_info ? '已保存' : '未获取到'
      });
    }
    return res;
  }).catch(error => {
    console.error('Staff Password Login Error:', error);
    throw error;
  });
};

/**
 * 服务人员短信验证码登录
 * @param {Object} data - 登录参数
 * @param {string} data.mobile - 手机号
 * @param {string} data.sms_code - 短信验证码
 * @returns {Promise} - 返回登录结果
 */
export const staffSmsLogin = (data) => {
  const { mobile, sms_code } = data;
  return post('/api/v1/staff-login/sms', { mobile, sms_code }).then(res => {
    // 处理响应数据，保存token到Vuex
    if (res.access_token) {
      store.commit('Updates', {
        token: res.access_token,
        isLogin: true,
        currentRole: 'staff',
        staffInfo: res.staff_info,
        access_token: res.access_token  // 同时设置access_token
      });

      // 保存到本地存储 - 员工端使用独立的token键
      uni.setStorageSync('staffToken', res.access_token);
      uni.setStorageSync('access_token', res.access_token); // 新增access_token存储
      uni.setStorageSync('token', res.access_token); // 兼容性保留
      uni.setStorageSync('isLogin', true);
      uni.setStorageSync('currentRole', 'staff');
      uni.setStorageSync('staffInfo', res.staff_info);

      console.log('员工短信登录成功，token已保存:', {
        staffToken: '已保存',
        currentRole: 'staff',
        staffInfo: res.staff_info ? '已保存' : '未获取到'
      });
    }
    return res;
  }).catch(error => {
    console.error('Staff SMS Login Error:', error);
    throw error;
  });
};

/**
 * 服务人员微信登录
 * @param {Object} data - 登录参数
 * @param {string} data.wx_code - 微信授权码
 * @returns {Promise} - 返回登录结果
 */
export const staffWechatLogin = (data) => {
  const { wx_code } = data;
  return post('/api/v1/staff-login/wechat', { wx_code }).then(res => {
    // 处理响应数据，保存token到Vuex
    if (res.access_token) {
      store.commit('Updates', {
        token: res.access_token,
        isLogin: true,
        currentRole: 'staff',
        staffInfo: res.staff_info,
        staffToken: res.access_token,
        access_token: res.access_token  // 确保access_token也被设置
      });

      // 保存到本地存储 - 员工端使用独立的token键
      uni.setStorageSync('staffToken', res.access_token);
      uni.setStorageSync('access_token', res.access_token); // 确保access_token也被保存
      uni.setStorageSync('token', res.access_token); // 兼容性保留
      uni.setStorageSync('isLogin', true);
      uni.setStorageSync('currentRole', 'staff');
      uni.setStorageSync('staffInfo', res.staff_info);

      console.log('员工微信登录成功，token已保存:', {
        staffToken: '已保存',
        access_token: '已保存',
        currentRole: 'staff',
        staffInfo: res.staff_info ? '已保存' : '未获取到'
      });
    }
    return res;
  }).catch(error => {
    console.error('Staff Wechat Login Error:', error);
    throw error;
  });
};

/**
 * 员工手机号快速验证登录
 * @param {Object} data - 登录参数
 * @param {string} data.wx_code - 微信授权码
 * @param {string} data.encrypted_data - 加密的手机号数据
 * @param {string} data.iv - 初始向量
 * @returns {Promise} - 返回登录结果
 */
export const staffPhoneQuickAuth = (data) => {
  const { wx_code, encrypted_data, iv } = data;
  console.log('员工手机号快速验证登录API调用，参数:', { wx_code, encrypted_data: '***', iv: '***' });

  return post('/api/v1/staff-login/phone-quick-auth', {
    wx_code,
    encrypted_data,
    iv
  }).then(res => {
    console.log('员工手机号快速验证登录API返回结果:', res);

    // 处理响应数据，保存token到Vuex
    if (res.access_token) {
      // 先清理所有旧的token，确保使用最新的token
      try {
        uni.removeStorageSync('staffToken');
        uni.removeStorageSync('access_token');
        uni.removeStorageSync('token');
        console.log('已清理旧的token');
      } catch (error) {
        console.warn('清理旧token失败:', error);
      }

      store.commit('Updates', {
        token: res.access_token,
        isLogin: true,
        currentRole: 'staff',
        staffInfo: res.staff_info,
        staffToken: res.access_token,
        access_token: res.access_token  // 确保access_token也被设置
      });

      // 保存到本地存储 - 员工端使用独立的token键
      uni.setStorageSync('staffToken', res.access_token);
      uni.setStorageSync('access_token', res.access_token); // 确保access_token也被保存
      uni.setStorageSync('token', res.access_token); // 兼容性保留
      uni.setStorageSync('isLogin', true);
      uni.setStorageSync('currentRole', 'staff');
      uni.setStorageSync('staffInfo', res.staff_info);

      console.log('员工手机号快速验证登录成功，token已保存:', {
        staffToken: '已保存',
        access_token: '已保存',
        currentRole: 'staff',
        staffInfo: res.staff_info ? '已保存' : '未获取到'
      });
    }

    return res;
  }).catch(error => {
    console.error('Staff Phone Quick Auth Error:', error);
    throw error;
  });
};

/**
 * 服务人员微信绑定
 * @param {Object} data - 绑定参数
 * @param {string} data.wx_openid - 微信OpenID
 * @param {string} data.mobile - 手机号
 * @param {string} data.sms_code - 短信验证码
 * @returns {Promise} - 返回绑定结果
 */
export const staffWechatBind = (data) => {
  const { wx_openid, mobile, sms_code } = data;
  return post('/api/v1/staff-login/wechat-bind', { wx_openid, mobile, sms_code }).then(res => {
    // 处理响应数据，保存token到Vuex
    if (res.access_token) {
      store.commit('Updates', {
        token: res.access_token,
        isLogin: true,
        currentRole: 'staff',
        staffInfo: res.staff_info,
        staffToken: res.access_token,
        access_token: res.access_token  // 确保access_token也被设置
      });

      // 保存到本地存储 - 员工端使用独立的token键
      uni.setStorageSync('staffToken', res.access_token);
      uni.setStorageSync('access_token', res.access_token); // 确保access_token也被保存
      uni.setStorageSync('token', res.access_token); // 兼容性保留
      uni.setStorageSync('isLogin', true);
      uni.setStorageSync('currentRole', 'staff');
      uni.setStorageSync('staffInfo', res.staff_info);

      console.log('员工微信绑定成功，token已保存:', {
        staffToken: '已保存',
        access_token: '已保存',
        currentRole: 'staff',
        staffInfo: res.staff_info ? '已保存' : '未获取到'
      });
    }
    return res;
  }).catch(error => {
    console.error('Staff Wechat Bind Error:', error);
    throw error;
  });
};

/**
 * 获取服务人员信息
 * @returns {Promise} - 返回用户信息
 */
export const getStaffInfo = () => {
  return get('/api/v1/staff-login/getinfo').then(res => {
    // 更新Vuex中的用户信息
    if (res.staff_info) {
      store.commit('Updates', { staffInfo: res.staff_info });
      uni.setStorageSync('staffInfo', res.staff_info);
    }
    return res;
  }).catch(error => {
    console.error('Get Staff Info Error:', error);
    throw error;
  });
};

/**
 * 获取门店联系方式
 * @returns {Promise} - 返回门店联系方式信息
 */
export const getStoreContact = () => {
  return get('/api/v1/staff-login/get-store-contact').then(res => {
    console.log('获取门店联系方式成功:', res);
    return res;
  }).catch(error => {
    console.error('获取门店联系方式失败:', error);
    throw error;
  });
};

/**
 * 验证员工公司访问权限（不修改后端数据）
 * @param {Object} data - 参数
 * @param {string} data.company_id - 公司ID
 * @returns {Promise} - 返回结果
 */
export const validateCompanyAccess = (data) => {
  const { company_id } = data;
  console.log('validateCompanyAccess API调用，参数:', { company_id });

  // 后端接口使用Form格式，需要设置正确的Content-Type
  return post('/api/v1/staff-login/validate-company', { company_id }, {
    contentType: 'application/x-www-form-urlencoded'
  }).then(res => {
    console.log('validateCompanyAccess API返回结果:', res);
    return res;
  }).catch(error => {
    console.error('Validate Company Access Error:', error);
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      response: error.response
    });
    throw error;
  });
};

/**
 * 设置员工默认公司
 * @param {Object} data - 参数
 * @param {string} data.company_id - 公司ID
 * @returns {Promise} - 返回结果
 */
export const setDefaultCompany = (data) => {
  const { company_id } = data;
  console.log('setDefaultCompany API调用，参数:', { company_id });

  // 后端接口使用Form格式，需要设置正确的Content-Type
  return post('/api/v1/staff-login/set-default-company', { company_id }, {
    contentType: 'application/x-www-form-urlencoded'
  }).then(res => {
    console.log('setDefaultCompany API返回结果:', res);
    return res;
  }).catch(error => {
    console.error('Set Default Company Error:', error);
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      response: error.response
    });
    throw error;
  });
};

/**
 * 服务人员登出
 * @returns {Promise} - 返回结果
 */
export const staffLogout = () => {
  return post('/api/v1/staff-login/logout', {}).then(res => {
    // 清除本地存储的登录信息
    store.commit('Updates', {
      token: '',
      isLogin: false,
      currentRole: '',
      staffInfo: null
    });

    // 清除员工端相关的存储
    uni.removeStorageSync('staffToken');
    uni.removeStorageSync('token'); // 兼容性清除
    uni.removeStorageSync('isLogin');
    uni.removeStorageSync('currentRole');
    uni.removeStorageSync('staffInfo');

    console.log('员工登出成功，已清除所有token和用户信息');

    return res;
  }).catch(error => {
    console.error('Staff Logout Error:', error);
    throw error;
  });
};

/**
 * 员工重置密码
 * @param {Object} data - 重置密码参数
 * @param {string} data.mobile - 手机号
 * @param {string} data.verify_code - 短信验证码
 * @param {string} data.new_password - 新密码
 * @param {string} data.confirm_password - 确认密码
 * @returns {Promise} - 返回重置结果
 */
export const staffResetPassword = (data) => {
  const { mobile, verify_code, new_password, confirm_password } = data;
  console.log('员工重置密码API调用，参数:', { mobile, verify_code: '***', new_password: '***', confirm_password: '***' });

  // 后端接口使用Form格式，需要设置正确的Content-Type
  return post('/api/v1/staff-login/reset-password', {
    mobile,
    verify_code,
    new_password,
    confirm_password
  }, {
    contentType: 'application/x-www-form-urlencoded'
  }).then(res => {
    console.log('员工重置密码API返回结果:', res);
    return res;
  }).catch(error => {
    console.error('Staff Reset Password Error:', error);
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      response: error.response
    });
    throw error;
  });
};

/**
 * 检查手机号下是否有员工账号
 * @param {Object} data - 参数
 * @param {string} data.mobile - 手机号
 * @returns {Promise} - 返回检查结果
 */
export const checkStaffAccount = (data) => {
  const { mobile } = data;
  console.log('检查员工账号API调用，参数:', { mobile });

  return post('/api/v1/staff-login/check-account', { mobile }, {
    contentType: 'application/x-www-form-urlencoded'
  }).then(res => {
    console.log('检查员工账号API返回结果:', res);
    return res;
  }).catch(error => {
    console.error('Check Staff Account Error:', error);
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      response: error.response
    });
    throw error;
  });
};

/**
 * 切换到员工账号（获取默认员工账号token）
 * @param {Object} data - 参数
 * @param {string} data.mobile - 手机号
 * @returns {Promise} - 返回切换结果
 */
export const switchToStaffAccount = (data) => {
  const { mobile } = data;
  console.log('切换到员工账号API调用，参数:', { mobile });

  return post('/api/v1/staff-login/switch-account', { mobile }, {
    contentType: 'application/x-www-form-urlencoded'
  }).then(res => {
    console.log('切换到员工账号API返回结果:', res);

    // 处理响应数据，保存token到Vuex
    if (res.access_token) {
      store.commit('Updates', {
        token: res.access_token,
        isLogin: true,
        currentRole: 'staff',
        staffInfo: res.staff_info,
        access_token: res.access_token  // 同时设置access_token
      });

      // 保存到本地存储 - 员工端使用独立的token键
      uni.setStorageSync('staffToken', res.access_token);
      uni.setStorageSync('access_token', res.access_token); // 新增access_token存储
      uni.setStorageSync('token', res.access_token); // 兼容性保留
      uni.setStorageSync('isLogin', true);
      uni.setStorageSync('currentRole', 'staff');
      uni.setStorageSync('staffInfo', res.staff_info);

      console.log('切换到员工账号成功，token已保存:', {
        staffToken: '已保存',
        currentRole: 'staff',
        staffInfo: res.staff_info ? '已保存' : '未获取到'
      });
    }

    return res;
  }).catch(error => {
    console.error('切换到员工账号失败:', error);
    throw error;
  });
};

/**
 * 切换到管理端（获取管理端token）
 * @param {Object} data - 参数
 * @param {string} data.mobile - 手机号
 * @returns {Promise} - 返回切换结果
 */
export const switchToStoreAccount = (data) => {
  const { mobile } = data;
  console.log('切换到管理端API调用，参数:', { mobile });

  return post('/api/v1/staff-login/switch-to-store', { mobile }, {
    contentType: 'application/x-www-form-urlencoded'
  }).then(res => {
    console.log('切换到管理端API返回结果:', res);

    // 处理响应数据，保存token到Vuex
    if (res.access_token) {
      store.commit('Updates', {
        token: res.access_token,
        isLogin: true,
        currentRole: 'store',
        user: res.user
      });

      // 保存到本地存储 - 管理端使用独立的token键
      uni.setStorageSync('token', res.access_token);
      uni.setStorageSync('isLogin', true);
      uni.setStorageSync('currentRole', 'store');
      uni.setStorageSync('user', res.user);

      console.log('切换到管理端成功，token已保存:', {
        token: '已保存',
        currentRole: 'store',
        user: res.user ? '已保存' : '未获取到'
      });
    }

    return res;
  }).catch(error => {
    console.error('切换到管理端失败:', error);
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      response: error.response
    });

    // 确保错误信息能正确传递
    if (error.response && error.response.data && error.response.data.msg) {
      // 如果后端返回了具体的错误信息，使用后端的错误信息
      const backendError = new Error(error.response.data.msg);
      backendError.code = error.code;
      backendError.response = error.response;
      throw backendError;
    }

    throw error;
  });
};

// ==================== 微信公众号相关接口 ====================

/**
 * 生成员工微信公众号绑定二维码（参考门店端写法）
 * @param {Object} data - 参数
 * @param {string} data.staff_id - 员工ID
 * @param {number} data.expire_seconds - 二维码有效期（秒），默认30分钟
 * @returns {Promise} - 返回二维码信息
 */
export const generateStaffWechatQrCode = (data) => {
  const { staff_id, expire_seconds = 1800 } = data;
  console.log('生成员工微信公众号二维码API调用，参数:', { staff_id, expire_seconds });

  return post('/api/v1/staff-login/wechat-official/generate-qr-code', {
    staff_id,
    expire_seconds
  }, {
    contentType: 'application/json'
  }).then(res => {
    console.log('生成员工微信公众号二维码API返回结果:', res);

    // 参考门店端的数据处理方式，支持多种可能的数据结构
    let qrCodeUrl = '';
    if (res?.data?.qrCodeUrl) {
      qrCodeUrl = res.data.qrCodeUrl;
    } else if (res?.data?.qr_code_url) {
      qrCodeUrl = res.data.qr_code_url;
    } else if (res?.qrCodeUrl) {
      qrCodeUrl = res.qrCodeUrl;
    } else if (res?.qr_code_url) {
      qrCodeUrl = res.qr_code_url;
    }

    // 返回统一格式的数据
    return {
      qrCodeUrl: qrCodeUrl,
      expire_seconds: res?.data?.expire_seconds || res?.expire_seconds || expire_seconds,
      ticket: res?.data?.ticket || res?.ticket
    };
  }).catch(error => {
    console.error('Generate Staff Wechat QR Code Error:', error);
    throw error;
  });
};

/**
 * 检查员工微信公众号绑定状态（参考门店端写法）
 * @returns {Promise} - 返回绑定状态信息
 */
export const checkStaffWechatBindStatus = () => {
  console.log('检查员工微信公众号绑定状态API调用');

  return get('/api/v1/staff-login/wechat-official/bind-status').then(res => {
    console.log('检查员工微信公众号绑定状态API返回结果:', res);

    // 参考门店端的数据处理方式，支持多种可能的数据结构
    const bindStatus = res?.data || res;

    return {
      isBound: bindStatus?.isBound || false,
      bindTime: bindStatus?.bindTime || null,
      wechatNickname: bindStatus?.wechatNickname || null,
      wechatAvatar: bindStatus?.wechatAvatar || null
    };
  }).catch(error => {
    console.error('Check Staff Wechat Bind Status Error:', error);
    throw error;
  });
};

/**
 * 员工微信公众号绑定（参考门店端写法）
 * @param {Object} data - 绑定参数
 * @param {string} data.mobile - 手机号
 * @param {string} data.verify_code - 短信验证码
 * @param {string} data.wx_openid - 微信OpenID
 * @param {string} data.wx_unionid - 微信UnionID（可选）
 * @param {string} data.wx_nickname - 微信昵称（可选）
 * @param {string} data.wx_avatar - 微信头像（可选）
 * @returns {Promise} - 返回绑定结果
 */
export const bindStaffWechatOfficial = (data) => {
  const { mobile, verify_code, wx_openid, wx_unionid, wx_nickname, wx_avatar } = data;
  console.log('员工微信公众号绑定API调用，参数:', { mobile, verify_code: '***', wx_openid, wx_unionid, wx_nickname, wx_avatar });

  return post('/api/v1/staff-login/wechat-official/bind', {
    mobile,
    verify_code,
    wx_openid,
    wx_unionid,
    wx_nickname,
    wx_avatar
  }, {
    contentType: 'application/json'
  }).then(res => {
    console.log('员工微信公众号绑定API返回结果:', res);
    return res;
  }).catch(error => {
    console.error('Bind Staff Wechat Official Error:', error);
    throw error;
  });
};

/**
 * 员工微信公众号解绑（参考门店端写法）
 * @param {Object} data - 解绑参数
 * @param {string} data.staff_id - 员工ID
 * @param {string} data.verify_code - 短信验证码
 * @returns {Promise} - 返回解绑结果
 */
export const unbindStaffWechatOfficial = (data) => {
  const { staff_id, verify_code } = data;
  console.log('员工微信公众号解绑API调用，参数:', { staff_id, verify_code: '***' });

  return post('/api/v1/staff-login/wechat-official/unbind', {
    staff_id,
    verify_code
  }, {
    contentType: 'application/json'
  }).then(res => {
    console.log('员工微信公众号解绑API返回结果:', res);
    return res;
  }).catch(error => {
    console.error('Unbind Staff Wechat Official Error:', error);
    throw error;
  });
};

/**
 * 刷新员工微信公众号二维码（便捷方法，参考门店端写法）
 * @param {string} staff_id - 员工ID
 * @param {number} expire_seconds - 二维码有效期（秒），默认30分钟
 * @returns {Promise} - 返回新的二维码信息
 */
export const refreshStaffWechatQrCode = (staff_id, expire_seconds = 1800) => {
  console.log('刷新员工微信公众号二维码，员工ID:', staff_id);

  return generateStaffWechatQrCode({
    staff_id,
    expire_seconds
  });
};

/**
 * 测试员工微信公众号推送（参考门店端写法）
 * @returns {Promise} - 返回推送结果
 */
export const testStaffWechatPush = () => {
  console.log('测试员工微信公众号推送API调用');

  return post('/api/v1/staff-login/wechat-official/test-push', {}, {
    contentType: 'application/json'
  }).then(res => {
    console.log('测试员工微信公众号推送API返回结果:', res);
    return res;
  }).catch(error => {
    console.error('Test Staff Wechat Push Error:', error);
    throw error;
  });
};


