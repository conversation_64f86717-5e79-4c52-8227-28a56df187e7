<template>
  <view class="page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 现代化头部 -->
    <view class="header-section">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 顶部导航 -->
        <view class="top-nav">
          <view class="nav-left" @click="handleBack">
            <view class="back-btn">
              <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
            </view>
          </view>
          <view class="nav-center">
            <text class="nav-title">代客预约</text>
          </view>
          <view class="nav-right"></view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content">
      <!-- 客户信息识别卡片 -->
      <view class="info-card identify-card">
        <view class="card-header">
          <view class="header-icon identify-icon">
            <u-icon name="scan" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">智能识别客户信息</text>
        </view>
        <view class="card-content">
          <view class="info-text">将客户信息（包括姓名、手机号、地址）粘贴到输入框后可自动识别出来</view>
          <view class="input-area">
            <textarea v-model="customerInfo" placeholder="请输入客户信息" :maxlength="200" @input="handleInput"></textarea>
            <view class="voice-btn" :class="{recording: isRecording}" @click="toggleVoiceInput">
              <u-icon :name="isRecording ? 'pause-circle-fill' : 'mic'" size="18" :color="isRecording ? '#fff' : '#666'"></u-icon>
            </view>
            <view class="counter">{{ inputLength }}/200</view>
          </view>
          <view class="action-buttons">
            <view class="btn-secondary" @click="identifyCustomerInfo">
              <u-icon name="search" size="16" color="#666"></u-icon>
              <text>智能识别</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 客户信息卡片 -->
      <view class="info-card customer-card">
        <view class="card-header">
          <view class="header-icon customer-icon">
            <u-icon name="account" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">客户信息</text>
        </view>
        <view class="card-content">
          <view class="form-group">
            <text class="label">联系人姓名 <text class="required">*</text></text>
            <input v-model="parsedInfo.name" placeholder="请输入联系人姓名" class="input-field" />
          </view>
          <view class="form-group">
            <text class="label">联系方式 <text class="required">*</text></text>
            <input v-model="parsedInfo.phone" placeholder="请输入手机号码" class="input-field" type="number" />
          </view>
        </view>
      </view>

      <!-- 服务地址卡片 -->
      <view class="info-card address-card">
        <view class="card-header">
          <view class="header-icon address-icon">
            <u-icon name="map" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">服务地址</text>
        </view>
        <view class="card-content">
          <view class="form-group">
            <text class="label">服务地址 <text class="required">*</text></text>
            <view class="address-input-container">
              <textarea v-model="parsedInfo.address" placeholder="请输入详细地址" class="address-input"></textarea>
              <view class="map-btn" @click="selectLocation">
                <u-icon name="map" size="16" color="#666"></u-icon>
                <text>地图选点</text>
              </view>
            </view>
          </view>
          <view class="form-group">
            <text class="label">门牌号</text>
            <input v-model="doorNumber" placeholder="请输入门牌号（选填）" class="input-field" />
          </view>
        </view>
      </view>

      <!-- 服务项目卡片 -->
      <view class="info-card service-card">
        <view class="card-header">
          <view class="header-icon service-icon">
            <u-icon name="list" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">服务项目</text>
        </view>
        <view class="card-content">
          <view class="form-group">
            <text class="label">选择服务 <text class="required">*</text></text>
            <view class="service-selector" @click="selectService">
              <text class="service-text" :class="{ placeholder: !selectedSku.productName }">
                {{ selectedSku.productName || '请选择服务项目' }}
              </text>
              <u-icon name="arrow-right" size="16" color="#666"></u-icon>
            </view>
          </view>
          <view class="form-group">
            <text class="label">购买金额 <text class="required">*</text></text>
            <input v-model="amount" placeholder="请输入购买金额" class="input-field" type="digit" />
          </view>
        </view>
      </view>

      <!-- 服务时间卡片 -->
      <view class="info-card time-card">
        <view class="card-header">
          <view class="header-icon time-icon">
            <u-icon name="clock" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">服务时间</text>
        </view>
        <view class="card-content">
          <view class="time-row">
            <view class="time-item">
              <text class="label">服务日期 <text class="required">*</text></text>
              <view class="time-selector" @click="showDatePicker = true">
                <text class="time-text" :class="{ placeholder: !serviceDate }">
                  {{ serviceDate || '请选择日期' }}
                </text>
                <u-icon name="calendar" size="16" color="#666"></u-icon>
              </view>
            </view>
            <view class="time-item">
              <text class="label">服务时间 <text class="required">*</text></text>
              <view class="time-selector" @click="showTimePicker = true">
                <text class="time-text" :class="{ placeholder: !serviceTime }">
                  {{ serviceTime || '请选择时间' }}
                </text>
                <u-icon name="clock" size="16" color="#666"></u-icon>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 备注信息卡片 -->
      <view class="info-card remark-card">
        <view class="card-header">
          <view class="header-icon remark-icon">
            <u-icon name="edit-pen" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">备注信息</text>
        </view>
        <view class="card-content">
          <view class="form-group">
            <text class="label">服务提醒</text>
            <textarea v-model="serviceNotice" placeholder="请输入服务提醒（选填）" class="textarea-field"></textarea>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <view class="submit-btn" :class="{ disabled: submitting }" @click="handleSubmit">
          <text>{{ submitting ? '提交中...' : '创建预约' }}</text>
        </view>
      </view>
    </view>

    <!-- 日期选择器 -->
    <u-datetime-picker
      :show="showDatePicker"
      v-model="selectedDate"
      mode="date"
      :min-date="minDate"
      :max-date="maxDate"
      @confirm="onDateConfirm"
      @cancel="showDatePicker = false"
    ></u-datetime-picker>

    <!-- 时间选择器 -->
    <u-datetime-picker
      :show="showTimePicker"
      v-model="selectedTime"
      mode="time"
      @confirm="onTimeConfirm"
      @cancel="showTimePicker = false"
    ></u-datetime-picker>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { uploadFileToPublic } from '@/utlis/common.js'
import { voiceRecognizeByUrl, geocodeAddress } from '@/api/common.js'
import { get } from '@/utlis/require.js'

export default {
  data() {
    return {
      customerInfo: '', // 客户信息输入
      inputLength: 0, // 输入长度计数
      amount: '', // 购买金额
      serviceNotice: '', // 服务提醒
      doorNumber: '', // 门牌号
      // 支付方式相关
      paymentType: 'cash', // 支付方式：cash-现金支付
      // 语音识别相关（百度语音识别）
      recorderManager: null, // 原生录音管理器
      isRecording: false, // 是否正在录音
      recordingTimer: null, // 录音计时器
      recordingDuration: 0, // 录音时长（秒）
      maxRecordingDuration: 30, // 最大录音时长（秒）
      minRecordingDuration: 2, // 最小录音时长（秒）
      recordingFilePath: '', // 录音文件路径
      // 解析后的客户信息
      parsedInfo: {
        name: '', // 客户姓名
        phone: '', // 客户电话
        address: '', // 客户地址
      },
      // 地图选点信息
      selectedLocation: {
        latitude: 0,
        longitude: 0,
        address: '',
        name: ''
      },
      // 选中的服务SKU
      selectedSku: {
        skuId: '',
        productUuid: '',
        productName: '',
        skuName: '',
        price: 0
      },
      // 服务时间相关
      serviceDate: '',
      serviceTime: '',
      showDatePicker: false,
      showTimePicker: false,
      selectedDate: '',
      selectedTime: '',
      minDate: new Date().getTime(),
      maxDate: new Date().getTime() + 90 * 24 * 60 * 60 * 1000, // 90天后
      // 时间选项
      timeOptions: [
        '08:00', '09:00', '10:00', '11:00', '12:00', '13:00',
        '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'
      ],
      // 提交状态
      submitting: false,
      // 门店信息
      storeInfo: null, // 门店信息
      storeBusinessHours: '08:00-18:00', // 门店营业时间，默认值
    };
  },

  computed: {
    ...mapState(['StatusBar', 'staffInfo'])
  },

  onReady() {
    this.initRecorder();
  },

  onLoad(options) {
    console.log('员工端代客预约页面接收到的参数:', options);
    
    // 如果传入了clientId，获取客户信息并自动填入
    if (options.clientId) {
      this.loadClientInfo(options.clientId);
    }
  },

  onShow() {
    // 监听SKU选择事件（备用机制）
    uni.$on('skuSelected', this.handleSkuSelected);

    // 检查存储中的SKU数据（主要机制）
    this.checkStoredSku();

    // 获取门店信息，用于设置服务时间选项
    this.loadStoreInfo();
  },

  onHide() {
    // 移除事件监听
    uni.$off('skuSelected', this.handleSkuSelected);
  },

  beforeDestroy() {
    // 清理录音管理器
    if (this.recorderManager) {
      this.recorderManager.stop();
    }
    // 清理定时器
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }
  },

  methods: {
    // 返回上一页
    handleBack() {
      uni.navigateBack();
    },

    // 输入框内容变化处理
    handleInput(e) {
      this.inputLength = e.detail.value.length;
    },

    // 初始化录音管理器
    initRecorder() {
      try {
        this.recorderManager = uni.getRecorderManager();

        this.recorderManager.onStart(() => {
          console.log('录音开始');
          this.isRecording = true;
          this.recordingDuration = 0;
          this.startRecordingTimer();
        });

        this.recorderManager.onStop((res) => {
          console.log('录音结束', res);
          this.isRecording = false;
          this.clearRecordingTimer();

          if (res.tempFilePath) {
            this.recordingFilePath = res.tempFilePath;
            this.processVoiceRecognition(res.tempFilePath);
          }
        });

        this.recorderManager.onError((err) => {
          console.error('录音错误:', err);
          this.isRecording = false;
          this.clearRecordingTimer();
          uni.showToast({
            title: '录音失败，请重试',
            icon: 'none'
          });
        });
      } catch (error) {
        console.error('初始化录音管理器失败:', error);
      }
    },

    // 切换语音输入
    toggleVoiceInput() {
      if (this.isRecording) {
        this.stopRecording();
      } else {
        this.startRecording();
      }
    },

    // 开始录音
    startRecording() {
      try {
        this.recorderManager.start({
          duration: this.maxRecordingDuration * 1000,
          sampleRate: 16000,
          numberOfChannels: 1,
          encodeBitRate: 96000,
          format: 'mp3'
        });
      } catch (error) {
        console.error('开始录音失败:', error);
        uni.showToast({
          title: '录音功能不可用',
          icon: 'none'
        });
      }
    },

    // 停止录音
    stopRecording() {
      try {
        this.recorderManager.stop();
      } catch (error) {
        console.error('停止录音失败:', error);
      }
    },

    // 开始录音计时
    startRecordingTimer() {
      this.recordingTimer = setInterval(() => {
        this.recordingDuration++;
        if (this.recordingDuration >= this.maxRecordingDuration) {
          this.stopRecording();
        }
      }, 1000);
    },

    // 清理录音计时器
    clearRecordingTimer() {
      if (this.recordingTimer) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = null;
      }
    },

    // 处理语音识别
    async processVoiceRecognition(filePath) {
      try {
        uni.showLoading({
          title: '正在识别语音...',
          mask: true
        });

        // 检查录音时长
        if (this.recordingDuration < this.minRecordingDuration) {
          throw new Error(`录音时长不能少于${this.minRecordingDuration}秒`);
        }

        // 上传录音文件并进行语音识别
        const recognizeResult = await this.uploadRecordingFile(filePath);

        uni.hideLoading();

        if (recognizeResult && recognizeResult.text) {
          // 将识别结果填入输入框
          this.customerInfo = recognizeResult.text;
          this.inputLength = this.customerInfo.length;

          uni.showToast({
            title: '语音识别成功',
            icon: 'success',
            duration: 1500
          });

          // 自动进行信息识别
          setTimeout(() => {
            this.identifyCustomerInfo();
          }, 1000);
        } else {
          throw new Error('语音识别失败，请重试');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('语音识别处理失败:', error);
        uni.showToast({
          title: error.message || '语音识别失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 上传录音文件并进行语音识别
    async uploadRecordingFile(filePath) {
      try {
        // 第一步：使用公共文件上传方法（无需token验证）
        const uploadResult = await uploadFileToPublic(filePath, {
          showLoading: false, // 我们在外层已经显示了加载提示
          showToast: false    // 我们在外层处理提示
        });

        // 第二步：调用语音识别接口
        const recognizeResult = await this.callVoiceRecognizeAPI(uploadResult.data);

        return recognizeResult;
      } catch (error) {
        console.error('语音识别流程失败:', error);
        throw error;
      }
    },

    // 调用语音识别API
    async callVoiceRecognizeAPI(fileUrl) {
      try {
        console.log('调用语音识别API:', fileUrl);

        // 调用后端语音识别接口
        const result = await voiceRecognizeByUrl(fileUrl);

        console.log('语音识别API响应:', result);

        // 检查响应数据结构
        if (result && result.text) {
          return {
            text: result.text,
            confidence: result.confidence || 0
          };
        } else {
          throw new Error('语音识别返回数据格式错误');
        }
      } catch (error) {
        console.error('语音识别API调用失败:', error);

        // 处理错误信息
        let errorMessage = '语音识别失败';
        if (error.message) {
          errorMessage = error.message;
        } else if (error.msg) {
          errorMessage = error.msg;
        }

        throw new Error(errorMessage);
      }
    },

    // 智能识别客户信息
    identifyCustomerInfo() {
      if (!this.customerInfo || !this.customerInfo.trim()) {
        uni.showToast({
          title: '请先输入客户信息',
          icon: 'none'
        });
        return;
      }

      try {
        const text = this.customerInfo.trim();
        const identifiedInfo = this.parseCustomerInfo(text);

        // 应用识别结果
        this.applyIdentificationResult(identifiedInfo);
      } catch (error) {
        console.error('客户信息识别失败:', error);
        uni.showToast({
          title: '信息识别失败，请检查输入格式',
          icon: 'none'
        });
      }
    },

    // 解析客户信息
    parseCustomerInfo(text) {
      const result = {
        name: '',
        phone: '',
        address: '',
        doorNumber: ''
      };

      // 提取手机号（11位数字，以1开头）
      const phoneRegex = /1[3-9]\d{9}/g;
      const phoneMatches = text.match(phoneRegex);
      if (phoneMatches && phoneMatches.length > 0) {
        result.phone = phoneMatches[0];
      }

      // 提取姓名（通常在手机号前面，2-4个中文字符）
      const nameRegex = /[\u4e00-\u9fa5]{2,4}/g;
      const nameMatches = text.match(nameRegex);
      if (nameMatches && nameMatches.length > 0) {
        // 取第一个匹配的中文名字
        result.name = nameMatches[0];
      }

      // 提取地址（包含省市区街道等关键词的文本）
      const addressKeywords = ['省', '市', '区', '县', '街道', '路', '号', '小区', '大厦', '楼', '室', '栋', '单元'];
      const addressRegex = new RegExp(`[\\u4e00-\\u9fa5\\d\\s]+(?:${addressKeywords.join('|')})[\\u4e00-\\u9fa5\\d\\s]*`, 'g');
      const addressMatches = text.match(addressRegex);
      if (addressMatches && addressMatches.length > 0) {
        result.address = addressMatches[0].trim();
      }

      // 提取门牌号（数字+号/室/楼等）
      const doorNumberRegex = /(\d+(?:号|室|楼|栋|单元))/g;
      const doorMatches = text.match(doorNumberRegex);
      if (doorMatches && doorMatches.length > 0) {
        result.doorNumber = doorMatches[doorMatches.length - 1]; // 取最后一个匹配
      }

      return result;
    },

    // 应用识别结果到表单
    applyIdentificationResult(identifiedInfo) {
      const { name, phone, address, doorNumber } = identifiedInfo;

      // 应用识别结果到表单
      if (name) this.parsedInfo.name = name;
      if (phone) this.parsedInfo.phone = phone;
      if (address) this.parsedInfo.address = address;
      if (doorNumber) this.doorNumber = doorNumber;

      let successMessage = '识别成功！';
      if (name) successMessage += ` 姓名：${name}`;
      if (phone) successMessage += ` 电话：${phone}`;
      if (address) {
        successMessage += ` 地址已解析`;
        // 如果识别到地址，尝试获取经纬度
        this.geocodeAddress(address);
      }
      if (doorNumber) successMessage += ` 门牌号：${doorNumber}`;

      uni.showToast({
        title: successMessage,
        icon: 'success',
        duration: 2000
      });
    },

    // 地址转经纬度（地理编码）
    async geocodeAddress(address) {
      if (!address || !address.trim()) {
        return;
      }

      try {
        console.log('开始地理编码:', address);

        // 调用后端地理编码API
        const result = await this.callGeocodeAPI(address);

        if (result && result.location) {
          // 更新地图选点信息
          this.selectedLocation = {
            latitude: result.location.lat,
            longitude: result.location.lng,
            address: address,
            name: result.formatted_addresses ? result.formatted_addresses.recommend : address
          };

          console.log('地理编码成功:', this.selectedLocation);
        }
      } catch (error) {
        console.error('地理编码失败:', error);
        // 地理编码失败不影响主流程，只记录错误
      }
    },

    // 调用后端地理编码API
    async callGeocodeAPI(address) {
      try {
        console.log('调用后端地理编码API:', address);

        // 调用后端地理编码接口
        const result = await geocodeAddress(address);

        console.log('后端地理编码API响应:', result);

        // 检查响应数据结构
        if (result && result.result && result.result.location) {
          // 返回与原来兼容的数据格式
          return {
            location: result.result.location,
            formatted_addresses: result.result.formatted_addresses,
            address_components: result.result.address_components
          };
        } else {
          throw new Error('地理编码返回数据格式错误');
        }
      } catch (error) {
        console.error('后端地理编码API调用失败:', error);

        // 处理错误信息
        let errorMessage = '地理编码失败';
        if (error.message) {
          errorMessage = error.message;
        } else if (error.msg) {
          errorMessage = error.msg;
        }

        throw new Error(errorMessage);
      }
    },

    // 选择服务位置
    selectLocation() {
      uni.chooseLocation({
        success: (res) => {
          console.log('地图选点成功:', res);
          this.selectedLocation = {
            latitude: res.latitude,
            longitude: res.longitude,
            address: res.address,
            name: res.name
          };
          this.parsedInfo.address = res.address;

          uni.showToast({
            title: '地址选择成功',
            icon: 'success'
          });
        },
        fail: (err) => {
          console.error('地图选点失败:', err);
          uni.showToast({
            title: '地址选择失败',
            icon: 'none'
          });
        }
      });
    },

    // 选择服务项目
    selectService() {
      uni.navigateTo({
        url: '/pages-home/sku-select'
      });
    },

    // 检查存储中的SKU数据
    checkStoredSku() {
      try {
        const storedSku = uni.getStorageSync('selectedSku');
        if (storedSku) {
          console.log('从存储中获取到SKU数据:', storedSku);
          this.handleSkuSelected(storedSku);
          // 清除存储中的数据，避免重复使用
          uni.removeStorageSync('selectedSku');
        }
      } catch (error) {
        console.error('检查存储SKU数据失败:', error);
      }
    },

    // 处理SKU选择
    handleSkuSelected(skuData) {
      console.log('处理SKU选择:', skuData);

      if (skuData && skuData.skuId) {
        this.selectedSku = {
          skuId: skuData.skuId,
          productUuid: skuData.productUuid,
          productName: skuData.productName,
          skuName: skuData.skuName,
          price: skuData.price || 0
        };

        // 如果有价格信息，自动填入金额
        if (skuData.price && skuData.price > 0) {
          this.amount = skuData.price.toString();
        }

        uni.showToast({
          title: '服务项目选择成功',
          icon: 'success',
          duration: 1500
        });
      }
    },

    // 日期确认
    onDateConfirm(e) {
      console.log('日期选择确认:', e);

      try {
        let dateString = '';

        // 处理不同格式的日期数据
        if (e.value) {
          // 如果是时间戳
          if (typeof e.value === 'number') {
            const date = new Date(e.value);
            dateString = this.formatDate(date);
          }
          // 如果是字符串格式的日期
          else if (typeof e.value === 'string') {
            const date = new Date(e.value);
            if (!isNaN(date.getTime())) {
              dateString = this.formatDate(date);
            } else {
              // 如果是 "YYYY-MM-DD" 格式的字符串
              dateString = e.value;
            }
          }
          // 如果是Date对象
          else if (e.value instanceof Date) {
            dateString = this.formatDate(e.value);
          }
        }

        // 验证日期格式
        if (dateString && dateString !== 'NaN-NaN-NaN') {
          this.serviceDate = dateString;
          this.showDatePicker = false;

          uni.showToast({
            title: `已选择日期: ${this.serviceDate}`,
            icon: 'success',
            duration: 1500
          });
        } else {
          throw new Error('日期格式无效');
        }
      } catch (error) {
        console.error('日期确认失败:', error);
        uni.showToast({
          title: '日期选择失败，请重试',
          icon: 'none'
        });
      }
    },

    // 时间确认
    onTimeConfirm(e) {
      console.log('时间选择确认:', e);

      try {
        let timeString = '';

        // 处理不同格式的时间数据
        if (e.value) {
          // 如果是时间戳
          if (typeof e.value === 'number') {
            const time = new Date(e.value);
            timeString = this.formatTime(time);
          }
          // 如果是字符串格式的时间
          else if (typeof e.value === 'string') {
            // 尝试解析字符串时间
            const time = new Date(e.value);
            if (!isNaN(time.getTime())) {
              timeString = this.formatTime(time);
            } else {
              // 如果是 "HH:MM" 格式的字符串
              timeString = e.value;
            }
          }
          // 如果是Date对象
          else if (e.value instanceof Date) {
            timeString = this.formatTime(e.value);
          }
        }

        // 验证时间格式
        if (timeString && timeString !== 'NaN:NaN') {
          this.serviceTime = timeString;
          this.showTimePicker = false;

          uni.showToast({
            title: `已选择时间: ${this.serviceTime}`,
            icon: 'success',
            duration: 1500
          });
        } else {
          throw new Error('时间格式无效');
        }
      } catch (error) {
        console.error('时间确认失败:', error);
        uni.showToast({
          title: '时间选择失败，请重试',
          icon: 'none'
        });
      }
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 格式化时间
    formatTime(time) {
      const hours = String(time.getHours()).padStart(2, '0');
      const minutes = String(time.getMinutes()).padStart(2, '0');
      return `${hours}:${minutes}`;
    },

    // 提交表单
    async handleSubmit() {
      // 防止重复提交
      if (this.submitting) {
        return;
      }

      // 检查必填项
      const validationResult = this.validateForm();
      if (!validationResult.isValid) {
        uni.showToast({
          title: validationResult.message,
          icon: 'none',
          duration: 2000
        });
        return;
      }

      try {
        this.submitting = true;

        // 显示加载提示
        uni.showLoading({
          title: '正在创建预约...',
          mask: true
        });

        // 准备提交数据
        const submitData = this.prepareSubmitData();

        console.log('提交数据:', submitData);

        // 调用下单接口
        const result = await this.submitOrder(submitData);

        uni.hideLoading();

        // 显示成功提示
        uni.showToast({
          title: '预约创建成功',
          icon: 'success',
          duration: 2000
        });

        // 延迟跳转到订单详情或返回上一页
        setTimeout(() => {
          if (result && result.order_number) {
            // 如果有订单号，跳转到订单详情
            uni.redirectTo({
              url: `/pages-dispatch/order-detail-new?orderNumber=${result.order_number}`
            });
          } else {
            // 否则返回上一页
            uni.navigateBack();
          }
        }, 1500);

      } catch (error) {
        uni.hideLoading();
        console.error('提交预约失败:', error);
        uni.showToast({
          title: error.message || '创建预约失败',
          icon: 'none',
          duration: 2000
        });
      } finally {
        this.submitting = false;
      }
    },

    // 表单验证
    validateForm() {
      // 检查联系人姓名
      if (!this.parsedInfo.name || !this.parsedInfo.name.trim()) {
        return { isValid: false, message: '请输入联系人姓名' };
      }

      // 检查联系方式
      if (!this.parsedInfo.phone || !this.parsedInfo.phone.trim()) {
        return { isValid: false, message: '请输入联系方式' };
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.parsedInfo.phone.trim())) {
        return { isValid: false, message: '请输入正确的手机号码' };
      }

      // 检查服务地址
      if (!this.parsedInfo.address || !this.parsedInfo.address.trim()) {
        return { isValid: false, message: '请选择或输入服务地址' };
      }

      // 检查选中的产品SKU
      if (!this.selectedSku || !this.selectedSku.skuId) {
        return { isValid: false, message: '请选择服务项目' };
      }

      // 检查购买金额
      if (!this.amount || parseFloat(this.amount) <= 0) {
        return { isValid: false, message: '请输入正确的购买金额' };
      }

      // 检查服务日期
      if (!this.serviceDate) {
        return { isValid: false, message: '请选择服务日期' };
      }

      // 检查服务时间
      if (!this.serviceTime) {
        return { isValid: false, message: '请选择服务时间' };
      }

      // 检查经纬度（如果有地址但没有经纬度，给出提示）
      if (!this.selectedLocation.latitude || !this.selectedLocation.longitude) {
        return { isValid: false, message: '请重新选择服务地址以获取准确位置' };
      }

      return { isValid: true, message: '' };
    },

    // 准备提交数据
    prepareSubmitData() {
      // 构建完整的服务地址
      let fullAddress = this.parsedInfo.address;
      if (this.doorNumber) {
        fullAddress += ` ${this.doorNumber}`;
      }

      // 验证必需的数据
      if (!this.selectedSku || !this.selectedSku.productUuid) {
        throw new Error('请先选择服务产品');
      }

      // 获取员工信息和门店信息
      const staffInfo = this.staffInfo || {};
      const currentCompany = this.$store.getters.getCurrentSelectedCompany || {};

      // 使用员工所属门店的UUID
      const storeUuid = currentCompany.store_uuid || staffInfo.store_uuid;
      if (!storeUuid) {
        throw new Error('无法获取门店信息，请重新登录');
      }

      // 使用代客下单接口格式构建数据
      const submitData = {
        // 1. 基础订单信息
        order_info: {
          amount: parseFloat(this.amount) || 0,
          buy_num: 1.0,
          pay_type: '106', // 现金支付
          service_date: this.serviceDate,
          service_time: this.serviceTime
        },

        // 2. 客户信息
        customer_info: {
          name: this.parsedInfo.name,
          phone: this.parsedInfo.phone,
          original_input: this.customerInfo
        },

        // 3. 服务地址信息
        address_info: {
          address: fullAddress,
          door_number: this.doorNumber || '',
          longitude: parseFloat(this.selectedLocation.longitude) || 0,
          latitude: parseFloat(this.selectedLocation.latitude) || 0,
          city: "厦门市",
          province: "福建省",
          district: ""
        },

        // 4. 产品信息
        product_info: {
          product_uuid: this.selectedSku.productUuid,
          sku_id: this.selectedSku.skuId,
          product_name: this.selectedSku.productName,
          sku_name: this.selectedSku.skuName || this.selectedSku.productName
        },

        // 5. 备注信息
        remark_info: {
          service_remark: this.serviceNotice || '',
          after_sale_remark: '',
          customer_remark: ''
        },

        // 6. 业务信息（员工代客预约特有）
        business_info: {
          order_source: 'staff_proxy', // 标识为员工代客预约
          channel_code: 'staff_app',
          area: '',
          house_type: ''
        }
      };

      return submitData;
    },

    // 提交订单到后端
    async submitOrder(orderData) {
      try {
        // 检查员工登录状态
        const staffToken = this.$store.state.staffToken || '';
        if (!staffToken) {
          throw new Error('员工未登录，请先登录');
        }

        // 手动将对象转换为JSON字符串
        const jsonData = JSON.stringify(orderData);
        console.log('发送的JSON数据:', jsonData);

        // 使用统一的请求工具发送POST请求
        const result = await this.$post('/api/v1/order/proxy-create', jsonData, {
          contentType: 'application/json'
        });

        console.log('员工代客预约接口响应:', result);
        return result;
      } catch (error) {
        console.error('员工代客预约接口请求失败:', error);

        // 根据错误类型进行处理
        if (error.code) {
          const errorMsg = this.handleOrderError(error.code, error.message);
          throw new Error(errorMsg);
        } else {
          throw new Error(`网络请求失败: ${error.message || '未知错误'}`);
        }
      }
    },

    // 处理订单错误
    handleOrderError(code, message) {
      const errorMap = {
        1001: '客户信息验证失败',
        1002: '服务地址信息不完整',
        1003: '产品信息无效',
        1004: '服务时间不可用',
        1005: '金额信息错误',
        1006: '门店信息获取失败',
        1007: '员工权限不足',
        1008: '订单创建失败',
        1009: '数据格式错误'
      };

      return errorMap[code] || message || '创建预约失败';
    },

    // 获取门店信息
    async loadStoreInfo() {
      try {
        console.log('开始获取门店信息...');

        // 调用门店信息接口
        const response = await get('/api/v1/order/storeInfo');

        if (response && response.business_hours) {
          this.storeInfo = response;
          console.log('获取门店信息成功:', this.storeInfo);

          // 更新营业时间
          this.storeBusinessHours = this.storeInfo.business_hours;
          console.log('门店营业时间:', this.storeBusinessHours);

          // 根据营业时间更新时间选项
          this.updateTimeOptions();
        } else {
          console.warn('获取门店信息失败，使用默认营业时间');
          // 使用默认时间选项
          this.updateTimeOptions();
        }
      } catch (error) {
        console.error('获取门店信息异常:', error);
        // 发生错误时使用默认时间选项
        this.updateTimeOptions();
      }
    },

    // 根据营业时间更新时间选项
    updateTimeOptions() {
      try {
        // 解析营业时间，格式如 "08:00-18:00"
        const businessHours = this.storeBusinessHours;
        const timeRange = businessHours.split('-');

        if (timeRange.length === 2) {
          const startTime = timeRange[0].trim();
          const endTime = timeRange[1].trim();

          // 生成时间选项
          const options = [];
          const start = parseInt(startTime.split(':')[0]);
          const end = parseInt(endTime.split(':')[0]);

          for (let hour = start; hour <= end; hour++) {
            options.push(`${String(hour).padStart(2, '0')}:00`);
          }

          this.timeOptions = options;
          console.log('更新时间选项:', this.timeOptions);
        }
      } catch (error) {
        console.error('解析营业时间失败:', error);
        // 使用默认时间选项
        this.timeOptions = [
          '08:00', '09:00', '10:00', '11:00', '12:00', '13:00',
          '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'
        ];
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 员工端代客预约页面样式
.page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f7fb 0%, #ffffff 100%);
  padding-bottom: 120rpx;
}

// 头部样式
.header-section {
  position: relative;
  height: calc(var(--status-bar-height) + 120rpx);
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header-content {
  position: relative;
  z-index: 2;
  padding-top: var(--status-bar-height);
}

.top-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 120rpx;
  padding: 0 40rpx;
}

.nav-left, .nav-right {
  width: 120rpx;
  display: flex;
  align-items: center;
}

.back-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.nav-center {
  flex: 1;
  text-align: center;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

// 内容区域
.content {
  padding: 40rpx 30rpx;
  margin-top: -40rpx;
  position: relative;
  z-index: 3;
}

// 信息卡片通用样式
.info-card {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx 20rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
}

.header-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.identify-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.customer-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.address-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.service-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.time-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.remark-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-content {
  padding: 30rpx 40rpx 40rpx;
}

// 识别卡片特有样式
.info-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.input-area {
  position: relative;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.input-area textarea {
  width: 100%;
  min-height: 120rpx;
  font-size: 30rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
}

.voice-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.voice-btn.recording {
  background: #ff4757;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.counter {
  position: absolute;
  bottom: 20rpx;
  right: 30rpx;
  font-size: 24rpx;
  color: #999;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.btn-secondary {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.btn-secondary:active {
  background: #e9ecef;
  transform: scale(0.98);
}

// 表单样式
.form-group {
  margin-bottom: 30rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.required {
  color: #ff4757;
}

.input-field {
  width: 100%;
  height: 88rpx;
  padding: 0 30rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  font-size: 30rpx;
  color: #333;
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: #667eea;
  background: #fff;
}

.textarea-field {
  width: 100%;
  min-height: 120rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  font-size: 30rpx;
  color: #333;
  resize: none;
  transition: all 0.3s ease;
}

.textarea-field:focus {
  border-color: #667eea;
  background: #fff;
}

// 地址输入特有样式
.address-input-container {
  position: relative;
}

.address-input {
  width: 100%;
  min-height: 120rpx;
  padding: 30rpx 120rpx 30rpx 30rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  font-size: 30rpx;
  color: #333;
  resize: none;
  transition: all 0.3s ease;
}

.address-input:focus {
  border-color: #667eea;
  background: #fff;
}

.map-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  font-size: 20rpx;
  color: #666;
}

// 服务选择器样式
.service-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.service-selector:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.service-text {
  font-size: 30rpx;
  color: #333;
}

.service-text.placeholder {
  color: #999;
}

// 时间选择样式
.time-row {
  display: flex;
  gap: 30rpx;
}

.time-item {
  flex: 1;
}

.time-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.time-selector:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.time-text {
  font-size: 30rpx;
  color: #333;
}

.time-text.placeholder {
  color: #999;
}

// 提交按钮样式
.submit-section {
  margin-top: 60rpx;
  padding: 0 20rpx;
}

.submit-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.submit-btn.disabled {
  background: #ccc;
  box-shadow: none;
  transform: none;
}
</style>
