import { get } from './require.js'
import store from '../vuex/index.js'
import { showMsg } from './common.js'

/**
 * 认证服务模块
 * 提供统一的token管理和认证状态检查
 */

// 不需要token验证的页面白名单
const AUTH_WHITELIST = [
	'pages/login/register',
	'pages-other/joinStore/process',
	'pages/login/login',  // 登录页本身也不需要验证
	'pages/aunt-resume/detail',  // 阿姨简历详情页（公开分享页面）
	'pages-public/lead-share',  // 线索分享页面（公开分享页面）
	'pages-large/training-list',  // 培训列表页面（支持分享免登录访问）
	'pages-large/training-detail',  // 培训详情页面（支持分享免登录访问）
	'pages-public/order-share',  // 订单分享页面（公开分享页面）
	'pages-home/attendantManage-add'  // 快速员工入驻页面（邀请码模式）
]

// 检查当前页面是否在白名单中
const isPageInWhitelist = () => {
	try {
		const pages = getCurrentPages()
		console.log('=== 白名单检查详细信息 ===')
		console.log('页面栈信息:', pages)
		console.log('页面栈长度:', pages ? pages.length : 0)

		if (pages && pages.length > 0) {
			const currentPage = pages[pages.length - 1]
			const currentRoute = currentPage.route

			console.log('当前页面对象:', currentPage)
			console.log('当前页面路由:', currentRoute)
			console.log('当前页面路由类型:', typeof currentRoute)
			console.log('当前页面路由长度:', currentRoute ? currentRoute.length : 0)
			console.log('白名单列表:', AUTH_WHITELIST)
			console.log('白名单列表长度:', AUTH_WHITELIST.length)

			// 特殊检查：直接测试线索分享页面
			const isLeadSharePage = currentRoute === 'pages-public/lead-share' || currentRoute.includes('lead-share')
			console.log('直接检查线索分享页面:', isLeadSharePage)

			// 检查是否在白名单中
			const isWhitelisted = AUTH_WHITELIST.some(whitelistPath => {
				// 使用更灵活的匹配逻辑：既支持包含匹配，也支持精确匹配
				const includesMatch = currentRoute.includes(whitelistPath)
				const exactMatch = currentRoute === whitelistPath
				const matches = includesMatch || exactMatch
				console.log(`检查路由 "${currentRoute}" 是否匹配 "${whitelistPath}": includes=${includesMatch}, exact=${exactMatch}, result=${matches}`)
				return matches
			})

			// 如果是线索分享页面，强制返回true
			const finalResult = isWhitelisted || isLeadSharePage

			console.log('页面是否在白名单中:', isWhitelisted)
			console.log('最终结果（包含特殊检查）:', finalResult)
			return finalResult
		} else {
			console.log('页面栈为空或未初始化，可能是应用启动初期')
			// 应用启动初期，页面栈可能未完全初始化
			// 此时应该允许应用正常启动，不强制跳转到登录页
			return false
		}
	} catch (error) {
		console.error('检查页面白名单失败:', error)
		return false
	}
}

// 检查token是否有效
export const validateToken = async () => {
	const currentRole = store?.state?.currentRole || uni.getStorageSync('currentRole') || 'store'

	// 根据当前角色选择对应的token和验证接口
	if (currentRole === 'staff') {
		return await validateStaffToken()
	} else {
		return await validateStoreToken()
	}
}

// 验证管理端token
export const validateStoreToken = async () => {
	const token = store?.state?.token

	if (!token) {
		console.log('没有找到管理端token')
		return false
	}

	try {
		// 调用现有的getInfo接口验证token有效性（使用GET方法）
		const result = await get('/api/v1/internal-login/getInfo', {}, { showErr: false })
		console.log('=== 管理端Token验证详细信息 ===')
		console.log('getInfo接口响应结果:', result)
		console.log('响应数据类型:', typeof result)
		console.log('响应数据内容:', JSON.stringify(result))

		// 如果获取用户信息成功，更新store中的用户信息并存储到本地缓存
		if (result && result.user) {
			console.log('更新用户信息:', result.user)
			store.commit('Updates', { user: result.user })
			// 存储用户信息到本地缓存
			try {
				uni.setStorageSync('userInfo', result.user)
				console.log('用户信息已存储到本地缓存')
			} catch (error) {
				console.error('存储用户信息到本地缓存失败:', error)
			}
		}

		console.log('管理端Token验证成功，返回true')
		return true
	} catch (error) {
		console.log('=== 管理端Token验证失败详细信息 ===')
		console.log('错误对象:', error)
		console.log('错误类型:', typeof error)
		console.log('错误内容:', JSON.stringify(error))

		// 检查错误类型，只有401、403视为token过期
		const isAuthError = error.code === 401 || error.code === 403

		if (isAuthError) {
			// 显示提示信息
			showMsg('您当前未登录或登录已过期，请重新登录')

			// 清除所有认证信息包括本地存储
			store.commit('clearAuth', true)
			// 清除本地缓存的用户信息
			try {
				uni.removeStorageSync('userInfo')
				uni.removeStorageSync('userPermissions')
				console.log('已清除本地缓存的用户信息')
			} catch (error) {
				console.error('清除本地缓存用户信息失败:', error)
			}
		} else {
			// 其他错误（如网络错误）不清除token
			console.log('网络错误或其他错误，不清除token')
		}

		return false
	}
}

// 验证员工端token
export const validateStaffToken = async () => {
	const staffToken = store?.state?.staffToken || uni.getStorageSync('staffToken')

	if (!staffToken) {
		console.log('没有找到员工端token')
		return false
	}

	try {
		// 调用员工端getinfo接口验证token有效性
		const result = await get('/api/v1/staff-login/getinfo', {}, { showErr: false })
		console.log('=== 员工端Token验证详细信息 ===')
		console.log('员工端getinfo接口响应结果:', result)
		console.log('响应数据类型:', typeof result)
		console.log('响应数据内容:', JSON.stringify(result))

		// 如果获取员工信息成功，更新store中的员工信息并存储到本地缓存
		if (result && result.staff_info) {
			console.log('更新员工信息:', result.staff_info)
			store.commit('Updates', {
				staffInfo: result.staff_info,
				staffToken: staffToken  // 确保staffToken在store中
			})
			// 存储员工信息到本地缓存
			try {
				uni.setStorageSync('staffInfo', result.staff_info)
				uni.setStorageSync('staffToken', staffToken)
				console.log('员工信息已存储到本地缓存')
			} catch (error) {
				console.error('存储员工信息到本地缓存失败:', error)
			}
		}
		if (result && result.permissions) {
			console.log('更新权限信息:', result.permissions)
			store.commit('Updates', { permissions: result.permissions })
			// 存储权限信息到本地缓存
			try {
				uni.setStorageSync('userPermissions', result.permissions)
				console.log('权限信息已存储到本地缓存')
			} catch (error) {
				console.error('存储权限信息到本地缓存失败:', error)
			}
		}

		console.log('员工端Token验证成功，返回true')
		return true
	} catch (error) {
		console.log('=== 员工端Token验证失败详细信息 ===')
		console.log('错误对象:', error)
		console.log('错误类型:', typeof error)
		console.log('错误内容:', JSON.stringify(error))

		// 检查错误类型，只有401、403视为token过期
		const isAuthError = error.code === 401 || error.code === 403

		if (isAuthError) {
			// 显示提示信息
			showMsg('您当前未登录或登录已过期，请重新登录')

			// 清除所有认证信息包括本地存储
			store.commit('clearAuth', true)
			// 清除本地缓存的员工信息
			try {
				uni.removeStorageSync('staffInfo')
				console.log('已清除本地缓存的员工信息')
			} catch (error) {
				console.error('清除本地缓存员工信息失败:', error)
			}
		} else {
			// 其他错误（如网络错误）不清除token
			console.log('网络错误或其他错误，不清除员工端token')
		}

		return false
	}
}

// 检查用户登录状态
export const checkAuthStatus = async () => {
	const token = store?.state?.token

	if (!token) {
		return {
			isAuthenticated: false,
			needLogin: true
		}
	}

	// 验证token有效性
	const isValid = await validateToken()

	return {
		isAuthenticated: isValid,
		needLogin: !isValid
	}
}

// 跳转到登录页
export const redirectToLogin = (showMessage = true) => {
	if (showMessage) {
		showMsg('您当前未登录或登录已过期，请重新登录')
	}

	// 延迟跳转，确保提示信息显示
	setTimeout(() => {
		// 统一跳转到登录页，登录页内部处理不同身份的登录
		console.log('跳转到统一登录页');
		uni.reLaunch({
			url: '/pages/login/login'
		});
	}, showMessage ? 1500 : 0)
}

// 应用启动时的认证检查
export const initAuthCheck = async () => {
	console.log('=== 应用启动认证检查开始 ===')
	console.log('检查本地token状态...')

	// 检查当前页面是否在白名单中
	const isWhitelisted = isPageInWhitelist()
	if (isWhitelisted) {
		console.log('✅ 当前页面在白名单中，跳过token验证')
		return true  // 白名单页面直接返回true，不进行token验证
	}

	const token = store?.state?.token
	console.log('Token状态:', token ? '存在' : '不存在')

	if (token) {
		console.log('发现本地token，验证token有效性...')

		try {
			// 直接验证token有效性
			const isValid = await validateToken()
			if (isValid) {
				console.log('✅ Token验证成功，用户已登录')
				return true
			} else {
				console.log('❌ Token验证失败，用户需要重新登录')
				return false
			}
		} catch (error) {
			console.log('❌ Token验证过程中出现错误:', error)
			return false
		}
	} else {
		console.log('❌ 没有本地token，用户需要登录')
		return false
	}
}

// 清除认证信息
export const clearAuth = () => {
	store.commit('clearAuth')
	// 清除本地缓存的用户信息
	try {
		uni.removeStorageSync('userInfo')
		uni.removeStorageSync('userPermissions')
		console.log('已清除本地认证信息和用户信息缓存')
	} catch (error) {
		console.error('清除本地缓存失败:', error)
	}
}

// 获取当前用户信息
export const getCurrentUser = () => {
	// 优先从Vuex获取，如果没有则从本地缓存获取
	let user = store?.state?.user
	let permissions = store?.state?.permissions

	// 如果Vuex中没有用户信息，尝试从本地缓存获取
	if (!user) {
		try {
			const cachedUser = uni.getStorageSync('userInfo')
			if (cachedUser) {
				user = cachedUser
				// 同时更新到Vuex
				store.commit('Updates', { user: cachedUser })
			}
		} catch (error) {
			console.error('从本地缓存获取用户信息失败:', error)
		}
	}

	// 如果Vuex中没有权限信息，尝试从本地缓存获取
	if (!permissions) {
		try {
			const cachedPermissions = uni.getStorageSync('userPermissions')
			if (cachedPermissions) {
				permissions = cachedPermissions
				// 同时更新到Vuex
				store.commit('Updates', { permissions: cachedPermissions })
			}
		} catch (error) {
			console.error('从本地缓存获取权限信息失败:', error)
		}
	}

	return {
		token: store?.state?.token || '',
		user: user || null,
		permissions: permissions || [],
		storeInfo: store?.state?.storeInfo || null,
		staffInfo: store?.state?.staffInfo || null,
		currentRole: store?.state?.currentRole || 'store'
	}
}

// 检查是否有特定权限
export const hasPermission = (permission) => {
	const permissions = store?.state?.permissions || []
	return permissions.includes(permission)
}

// 检查是否有双重身份
export const hasDualRole = () => {
	const { storeInfo, staffInfo } = getCurrentUser()
	return !!(storeInfo && staffInfo)
}

// 获取当前登录用户的基本信息（便捷方法）
export const getUserInfo = () => {
	const { user } = getCurrentUser()
	return user
}

// 清理指定角色的缓存信息
export const clearRoleSpecificCache = (targetRole) => {
	console.log(`=== 开始清理${targetRole}端缓存 ===`)

	try {
		if (targetRole === 'staff') {
			// 清理员工端特定缓存
			console.log('清理员工端缓存信息')
			uni.removeStorageSync('staffToken')
			uni.removeStorageSync('currentSelectedCompany')

			// 清理Vuex中的员工端token
			if (store) {
				store.commit('Updates', {
					staffToken: '',
					currentSelectedCompany: null
				})
			}

			console.log('员工端缓存清理完成')
		} else if (targetRole === 'store') {
			// 清理管理端特定缓存
			console.log('清理管理端缓存信息')
			uni.removeStorageSync('token')
			uni.removeStorageSync('user')
			uni.removeStorageSync('userInfo')
			uni.removeStorageSync('storeInfo')

			// 清理Vuex中的管理端信息
			if (store) {
				store.commit('Updates', {
					token: '',
					user: null,
					userInfo: null,
					storeInfo: null
				})
			}

			console.log('管理端缓存清理完成')
		}

		console.log(`${targetRole}端缓存清理操作完成`)
	} catch (error) {
		console.error(`清理${targetRole}端缓存失败:`, error)
	}
}

// 角色切换后的缓存整理
export const cleanupAfterRoleSwitch = (newRole) => {
	console.log(`=== 角色切换后缓存整理，新角色: ${newRole} ===`)

	// 清理对方角色的缓存
	const targetRole = newRole === 'store' ? 'staff' : 'store'
	clearRoleSpecificCache(targetRole)

	// 更新当前角色到本地存储和Vuex
	uni.setStorageSync('currentRole', newRole)
	if (store) {
		store.commit('Updates', { currentRole: newRole })
	}

	console.log(`角色切换缓存整理完成，当前角色: ${newRole}`)
}

// 获取当前登录用户的权限信息（便捷方法）
export const getUserPermissions = () => {
	const { permissions } = getCurrentUser()
	return permissions || []
}

// 获取当前登录用户的完整信息（包含缓存恢复）
export const getFullUserInfo = () => {
	return getCurrentUser()
}

export default {
	validateToken,
	checkAuthStatus,
	redirectToLogin,
	initAuthCheck,
	clearAuth,
	getCurrentUser,
	hasPermission,
	hasDualRole,
	getUserInfo,
	getUserPermissions,
	getFullUserInfo
}
